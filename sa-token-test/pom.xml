<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <packaging>pom</packaging>

	<!-- Sa-Token 单元测试合集 -->
	<name>sa-token-test</name>
    <artifactId>sa-token-test</artifactId>
	<description>sa-token-test</description>
	
	
	<!-- 所有子模块 -->
    <modules>
        <!-- <module>sa-token-core-test</module> -->
		<module>sa-token-springboot-test</module>
        <!-- <module>sa-token-springboot-integrate-test</module> -->
        <module>sa-token-jwt-test</module>
		<module>sa-token-temp-jwt-test</module>
		<module>sa-token-json-test</module>
		<module>sa-token-serializer-test</module>
    </modules>

	<dependencies>
		<!-- test -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<version>2.5.12</version>
			<scope>test</scope>
		</dependency>
		<!-- config -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
	</dependencies>

	<build>
	    <plugins>
	    	<!-- 单元测试报告生成 -->
	        <plugin>
	            <groupId>org.jacoco</groupId>
	            <artifactId>jacoco-maven-plugin</artifactId>
	            <version>0.8.6</version>
	            <executions>
	                <execution>
	                    <id>prepare-agent</id>
	                    <goals>
	                        <goal>prepare-agent</goal>
	                    </goals>
	                </execution>
	                <execution>
	                    <id>report-aggregate</id>
	                    <phase>test</phase>
	                    <goals>
	                        <goal>report-aggregate</goal>
	                    </goals>
	                </execution>
	            </executions>
	        </plugin>
	        <plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
	            <configuration>
	            	<!-- 命令行执行 mvn test 时默认字符集为GBK，与项目设置的utf-8造成冲突，所以这里需要指定字符集为utf-8 -->
	                <argLine>${argLine} -Xms256m -Xmx2048m -Dfile.encoding=utf-8</argLine>
	                <forkCount>1</forkCount>
	                <runOrder>random</runOrder>
	            </configuration>
	        </plugin>
	    </plugins>
	</build>
	
</project>

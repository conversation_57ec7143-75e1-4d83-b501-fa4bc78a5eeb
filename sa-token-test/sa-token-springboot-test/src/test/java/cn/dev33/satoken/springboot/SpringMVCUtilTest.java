/*
 * Copyright 2020-2099 sa-token.cc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.dev33.satoken.springboot;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import cn.dev33.satoken.exception.SaTokenException;
import cn.dev33.satoken.spring.SpringMVCUtil;

/**
 * SpringMVCUtil 测试 
 * 
 * <AUTHOR>  
 *
 */
public class SpringMVCUtilTest {

	// 开始 
	@BeforeAll
    public static void beforeClass() {
    	
    }

	// 结束 
    @AfterAll
    public static void afterClass() {
    	
    }

    // 测试，上下文 API 
    @Test
    public void testSaTokenContext() {
    	Assertions.assertThrows(SaTokenException.class, () -> SpringMVCUtil.getRequest());
    	Assertions.assertThrows(SaTokenException.class, () -> SpringMVCUtil.getResponse());
    	Assertions.assertFalse(SpringMVCUtil.isWeb());
    }

}

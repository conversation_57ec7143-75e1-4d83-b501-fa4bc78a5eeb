# token åç§° (åæ¶ä¹æ¯ cookie åç§°)
tokenName=use-token
# token æææï¼åä½ï¼ç§ï¼ é»è®¤30å¤©ï¼-1 ä»£è¡¨æ°¸ä¹ææ
timeout=9000
# token æä½æ´»è·é¢çï¼åä½ï¼ç§ï¼ï¼å¦æ token è¶è¿æ­¤æ¶é´æ²¡æè®¿é®ç³»ç»å°±ä¼è¢«å»ç»ï¼é»è®¤-1 ä»£è¡¨ä¸éå¶ï¼æ°¸ä¸å»ç»
activeTimeout=240
# æ¯å¦åè®¸åä¸è´¦å·å¤å°åæ¶ç»å½ ï¼ä¸º true æ¶åè®¸ä¸èµ·ç»å½, ä¸º false æ¶æ°ç»å½æ¤ææ§ç»å½ï¼
isConcurrent=false
# å¨å¤äººç»å½åä¸è´¦å·æ¶ï¼æ¯å¦å±ç¨ä¸ä¸ª token ï¼ä¸º true æ¶ææç»å½å±ç¨ä¸ä¸ª token, ä¸º false æ¶æ¯æ¬¡ç»å½æ°å»ºä¸ä¸ª tokenï¼
isShare=false
# æ¯å¦è¾åºæä½æ¥å¿ 
isLog=true
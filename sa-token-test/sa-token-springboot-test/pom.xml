<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-test</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <packaging>jar</packaging>

	<name>sa-token-springboot-test</name>
    <artifactId>sa-token-springboot-test</artifactId>
	<description>sa-token-springboot-test</description>

	<dependencies>
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-spring-boot-starter</artifactId>
        </dependency>
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-sso</artifactId>
            <scope>test</scope>
		</dependency>
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-oauth2</artifactId>
            <scope>test</scope>
		</dependency>
		<!-- 冗余（生成单元测试报告） -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-servlet</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-core</artifactId>
		</dependency>
	</dependencies>

</project>

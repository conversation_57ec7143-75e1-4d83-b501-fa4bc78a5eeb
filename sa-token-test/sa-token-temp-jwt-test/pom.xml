<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-test</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <packaging>jar</packaging>

	<name>sa-token-temp-jwt-test</name>
    <artifactId>sa-token-temp-jwt-test</artifactId>
	<description>sa-token-temp-jwt-test</description>

	<dependencies>
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-temp-jwt</artifactId>
		</dependency>
	</dependencies>

</project>

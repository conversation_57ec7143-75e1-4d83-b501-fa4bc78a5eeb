package com.pj.test.model;

import java.io.Serializable;

/**
 * Role 实体类
 * 
 * <AUTHOR>
 * @since 2022-10-15
 */
public class SysRole implements Serializable {
//
//	public SysRole() {
//	}
//
//	public SysRole(long id, String name) {
//		super();
//		this.id = id;
//		this.name = name;
//	}
//
//
//	/**
//	 * 角色id
//	 */
//	private long id;
//
//	/**
//	 * 角色名称
//	 */
//	private String name;
//
//	/**
//	 * @return id
//	 */
//	public long getId() {
//		return id;
//	}
//
//	/**
//	 * @param id 要设置的 id
//	 */
//	public void setId(long id) {
//		this.id = id;
//	}
//
//	/**
//	 * @return name
//	 */
//	public String getName() {
//		return name;
//	}
//
//	/**
//	 * @param name 要设置的 name
//	 */
//	public void setName(String name) {
//		this.name = name;
//	}
//
//	@Override
//	public String toString() {
//		return "SysRole [id=" + id + ", name=" + name + "]";
//	}
//
}

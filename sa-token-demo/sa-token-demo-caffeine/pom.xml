<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.dev33</groupId>
	<artifactId>sa-token-demo-caffeine</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	
	<!-- SpringBoot -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.5.14</version>
		<!-- <version>1.5.9.RELEASE</version> -->
		<relativePath/>
	</parent>
	
	<!-- 定义 Sa-Token 版本号 -->
	<properties>
		<sa-token.version>1.42.0</sa-token.version>
	</properties>

	<dependencies>

		<!-- SpringBoot依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		
		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${sa-token.version}</version>
        </dependency>

		<!-- Sa-Token 整合 Caffeine -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-caffeine</artifactId>
            <version>${sa-token.version}</version>
        </dependency>

		<!-- @ConfigurationProperties -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		
	</dependencies>
	
	
</project>
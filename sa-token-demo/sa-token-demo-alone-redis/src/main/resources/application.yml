# 端口
server:
    port: 8081

# Sa-Token配置
sa-token: 
    # Token名称 (同时也是cookie名称)
    token-name: satoken
    # Token有效期，单位s 默认30天, -1代表永不过期 
    timeout: 2592000
    # Token风格
    token-style: uuid
    # 配置Sa-Token单独使用的Redis连接 
    alone-redis:
        # Redis模式(默认单体)
        # pattern: single
        # Redis数据库索引（默认为0）
        database: 2
        # Redis服务器地址
        host: 127.0.0.1
        # Redis服务器连接端口
        port: 6379
        # Redis服务器连接密码（默认为空）
        # password: 
        # 连接超时时间（毫秒）
        timeout: 10s
        lettuce: 
            pool:
                # 连接池最大连接数
                max-active: 200
                # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms
                # 连接池中的最大空闲连接
                max-idle: 10
                # 连接池中的最小空闲连接
                min-idle: 0
        
spring: 
    # 配置业务使用的Redis连接 
    redis: 
        # Redis数据库索引（默认为0）
        database: 0
        # Redis服务器地址
        host: 127.0.0.1
        # Redis服务器连接端口
        port: 6379
        # Redis服务器连接密码（默认为空）
        password: 
        # 连接超时时间（毫秒）
        timeout: 10s
        lettuce: 
            pool:
                # 连接池最大连接数
                max-active: 200
                # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms
                # 连接池中的最大空闲连接
                max-idle: 10
                # 连接池中的最小空闲连接
                min-idle: 0
        
        
        
        
        
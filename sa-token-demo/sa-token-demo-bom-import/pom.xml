<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.dev33</groupId>
	<artifactId>sa-token-demo-bom-import</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	
	<!-- SpringBoot -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.5.14</version>
		<relativePath/>
	</parent>
	
	<!-- 定义 Sa-Token 版本号 -->
	<properties>
		<sa-token.version>1.42.0</sa-token.version>
	</properties>

	<dependencies>

		<!-- SpringBoot依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		
		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
        </dependency>

		<!-- Sa-Token整合 Redis (使用jackson序列化方式) -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
        </dependency>

		<!-- Sa-Token整合 jwt -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-jwt</artifactId>
		</dependency>

		<!-- 提供Redis连接池 -->
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        
		<!-- @ConfigurationProperties -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		
	</dependencies>


	<!-- 依赖管理 (不会真正的引入依赖，只会限定其版本) -->
	<dependencyManagement>
		<dependencies>

			<!-- Sa-Token 所有包的版本定义 -->
			<dependency>
				<groupId>cn.dev33</groupId>
				<artifactId>sa-token-bom</artifactId>
				<version>1.42.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

		</dependencies>
	</dependencyManagement>
	
</project>
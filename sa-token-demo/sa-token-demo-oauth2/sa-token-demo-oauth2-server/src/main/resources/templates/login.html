<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>Sa-OAuth2-认证中心-登录页</title>
		<style type="text/css">
			body{background-color: #F5F5D5;}
			*{margin: 0px; padding: 0px;}
			.login-box{width: 400px; margin: 20vh auto;}
			.login-box input{line-height: 25px; margin-bottom: 10px;}
			.login-box button{padding: 5px 15px; cursor: pointer; }
		</style>
	</head>
	<body>
		<div class="login-box">
			<h2>Sa-OAuth2-认证中心-登录页</h2> <br>
			账号：<input name="name" /> <br>
			密码：<input name="pwd" type="password" /> <br>
			<button onclick="doLogin()">登录</button>
			<span style="color: #666;">（测试账号： sa / 123456）</span>
		</div>
		<script src="https://unpkg.zhimg.com/jquery@3.4.1/dist/jquery.min.js"></script>
		<script src="https://www.layuicdn.com/layer-v3.1.0/layer.js"></script>
		<script>window.jQuery || alert('当前页面CDN服务商已宕机，请将所有js包更换为本地依赖')</script>
		<script type="text/javascript">
			
			// 登录方法 
			function doLogin() {
				console.log('-----------');
				$.ajax({
					url: '/oauth2/doLogin',
					data: {
						name: $('[name=name]').val(),
						pwd: $('[name=pwd]').val()
					},
					dataType: 'json', 
					success: function(res) {
						if(res.code == 200) {
							layer.msg('登录成功！');
							setTimeout(function() {
								location.reload(true);
							}, 800);
						} else {
							layer.alert(res.msg);
						}
					},
					error: function(e) {
						console.log('error');
					}
				});
			}
		</script>
	</body>
</html>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.pj</groupId>
	<artifactId>sa-token-demo-oauth2-server</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<!-- SpringBoot -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.5.14</version>
	</parent>

	<!-- 指定一些属性 -->
	<properties> 
		<java.version>1.8</java.version>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<!-- 定义 Sa-Token 版本号 -->
		<sa-token.version>1.42.0</sa-token.version>
	</properties>
	
	<dependencies>

		<!-- SpringBoot依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		
		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
		    <groupId>cn.dev33</groupId>
		    <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${sa-token.version}</version>
		</dependency>

		<!-- Sa-Token-OAuth2.0 模块 -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-oauth2</artifactId>
            <version>${sa-token.version}</version>
        </dependency>

		<!-- Sa-Token整合Redis (使用jackson序列化方式) -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        
		<!-- thymeleaf 视图引擎 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>

		<!-- sa-token-jwt 签发 OIDC id_token 令牌 -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-jwt</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

		<!-- 热刷新 -->
		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>provided</scope>
		</dependency>-->
        
		<!-- ConfigurationProperties -->
        <dependency>
        	<groupId>org.springframework.boot</groupId>
        	<artifactId>spring-boot-configuration-processor</artifactId>
        	<optional>true</optional>
        </dependency>
        
	</dependencies>




</project>
# 端口
server:
    port: 9003

# sa-token配置 
sa-token: 
    # SSO-相关配置
    sso-client:
        # SSO-Server端 主机地址
        server-url: http://sa-sso-server.com:9000
        # 使用Http请求校验ticket 
        is-http: true
        # 打开单点注销功能 
        is-slo: true
    sign:
        # 接口调用秘钥 
        secret-key: kQwIOrYvnXmSDkwEiFngrKidMcdrgKor


# 配置 Sa-Token Dao（此处与SSO-Server端连接不同的Redis）
sa-token.dao: #名字可以随意取
    redis:
        server: "localhost:6379"
#        password: 123456
        db: 2
        maxTotal: 200
        
forest: 
    # 关闭 forest 请求日志打印
    log-enabled: false
        
        
        
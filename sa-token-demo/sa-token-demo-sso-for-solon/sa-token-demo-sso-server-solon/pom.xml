<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.dev33</groupId>
	<artifactId>sa-token-demo-sso-server-solon</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	
	<!-- Solon -->
	<parent>
		<groupId>org.noear</groupId>
		<artifactId>solon-parent</artifactId>
		<version>3.0.1</version>
		<relativePath/>
	</parent>
	
	<!-- 定义 Sa-Token 版本号 -->
	<properties>
		<sa-token.version>1.42.0</sa-token.version>
	</properties>

	<dependencies>

		<!-- SpringBoot Web依赖 -->
		<dependency>
			<groupId>org.noear</groupId>
			<artifactId>solon-web</artifactId>
			<version>${solon.version}</version>
		</dependency>
		
		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-solon-plugin</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        
		<!-- Sa-Token 插件：整合SSO -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-sso</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        
		<!-- Sa-Token 插件：整合redis (使用jackson序列化方式) -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-redisx</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

		<!-- 视图引擎（在前后端不分离模式下提供视图支持） -->
		<dependency>
			<groupId>org.noear</groupId>
			<artifactId>solon.view.thymeleaf</artifactId>
			<version>${solon.version}</version>
		</dependency>
		
		<!-- Http请求工具（在模式三的单点注销功能下用到，如不需要可以注释掉） -->
		<dependency>
		    <groupId>org.noear</groupId>
		    <artifactId>forest-solon-plugin</artifactId>
			<version>${solon.version}</version>
		</dependency>



	</dependencies>
	
	
</project>
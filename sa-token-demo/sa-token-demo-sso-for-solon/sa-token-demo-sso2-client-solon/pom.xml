<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.dev33</groupId>
	<artifactId>sa-token-demo-sso2-client-solon</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<!-- Solon -->
	<parent>
		<groupId>org.noear</groupId>
		<artifactId>solon-parent</artifactId>
		<version>3.0.1</version>
		<relativePath/>
	</parent>
	
	<!-- 定义 Sa-Token 版本号 -->
	<properties>
		<sa-token.version>1.42.0</sa-token.version>
	</properties>

	<dependencies>

		<!-- Solon 依赖 -->
		<!--<dependency>
			<groupId>org.noear</groupId>
			<artifactId>solon-api</artifactId>
		</dependency>-->
		<dependency>
			<groupId>org.noear</groupId>
			<artifactId>solon-web</artifactId>
			<version>${solon.version}</version>
		</dependency>

		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-solon-plugin</artifactId>
			<version>${sa-token.version}</version>
		</dependency>
        
		<!-- Sa-Token 插件：整合SSO -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-sso</artifactId>
            <version>${sa-token.version}</version>
        </dependency>

		<!-- Sa-Token 整合 redisx -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-redisx</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

		<!-- Http 请求工具 -->
		<dependency>
			<groupId>org.noear</groupId>
			<artifactId>forest-solon-plugin</artifactId>
		</dependency>

	</dependencies>
	
	
</project>
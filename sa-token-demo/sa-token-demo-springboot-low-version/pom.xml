<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.dev33</groupId>
	<artifactId>sa-token-demo-springboot-low-version</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	
	<!-- SpringBoot -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<!-- 可以 -->
		<!--<version>2.2.0.RELEASE</version>-->
		<!-- 不可以 -->
		<version>2.1.18.RELEASE</version>
		<relativePath/>
	</parent>
	
	<!-- 定义 Sa-Token 版本号 -->
	<properties>
		<sa-token.version>1.42.0</sa-token.version>
		<java.run.main.class>com.pj.SaTokenApplication</java.run.main.class>
		<java.version>1.8</java.version>
	</properties>

	<dependencies>

		<!-- SpringBoot依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-spring-boot-starter</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.36</version>
		</dependency>

		<!-- Sa-Token 整合 RedisTemplate -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-redis-template</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

		<!-- 提供Redis连接池 -->
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

		<!-- SpringBoot 版本过低时，需要追加的包 (低于2.2.0时，不包含2.2.0本身) -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.17.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.17.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.17.3</version>
		</dependency>

        
		<!-- @ConfigurationProperties -->
		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>-->

	</dependencies>

	<!-- 构建配置 -->
	<build>
		<!-- 配置资源目录  -->
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
			</resource>
		</resources>
		<plugins>
			<!-- 打包jar文件时，配置manifest文件，加入lib包的jar依赖 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
							<mainClass>${java.run.main.class}</mainClass>
						</manifest>
					</archive>
				</configuration>
			</plugin>
			<!-- 拷贝依赖的jar包到lib目录 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>
								${project.build.directory}/lib
							</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	
</project>
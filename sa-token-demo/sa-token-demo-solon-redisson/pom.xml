<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.dev33</groupId>
	<artifactId>sa-token-demo-solon-redisson</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<!-- Solon -->
	<parent>
		<groupId>org.noear</groupId>
		<artifactId>solon-parent</artifactId>
		<version>3.0.1</version>
		<relativePath/>
	</parent>

	<!-- 定义 Sa-Token 版本号 -->
	<properties>
		<sa-token.version>1.42.0</sa-token.version>
	    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
	</properties>

	<dependencies>

		<!-- Solon 依赖 -->
		<dependency>
			<groupId>org.noear</groupId>
			<artifactId>solon-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.noear</groupId>
			<artifactId>solon.logging.simple</artifactId>
		</dependency>

		<dependency>
			<groupId>org.noear</groupId>
			<artifactId>redisson-solon-plugin</artifactId>
		</dependency>
		
		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-solon-plugin</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
		
		<!-- sa-token整合redis (使用jdk默认序列化方式) -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redisson</artifactId>
            <version>${sa-token.version}</version>
        </dependency>


		<!-- hutool工具类，用来生成雪花算法唯一id -->
		<!-- <dependency>
		     <groupId>cn.hutool</groupId>
		     <artifactId>hutool-all</artifactId>
		     <version>5.5.4</version>
		</dependency> -->
		

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<compilerArgument>-parameters</compilerArgument>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>
	
	
</project>